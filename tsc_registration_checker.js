async function checkTSCRegistration(idNumber) {
  try {
    // Use the proxy server instead of direct request
    const response = await fetch('/api/check-registration', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ idNumber: idNumber })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Unknown error occurred');
    }

    return result;
  } catch (error) {
    console.error('Error checking registration:', error);
    return null;
  }
}

// Example usage
checkTSCRegistration('12345678').then(result => {
  if (result && result.data) {
    console.log('Registration Status:', result.data.status);
    console.log('Message:', result.data.message);
    console.log('Details:', result.data.details);
  } else {
    console.log('No result received');
  }
});