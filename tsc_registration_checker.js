async function checkTSCRegistration(idNumber) {
  try {
    const url = 'http://tsconline.tsc.go.ke/register/registration-status';
    
    // Get the CSRF token from the page
    const response = await fetch(url);
    const html = await response.text();
    const csrfMatch = html.match(/<meta name="csrf-token" content="([^"]+)">/);
    const csrfToken = csrfMatch ? csrfMatch[1] : '';
    
    // Create form data with the ID number and CSRF token
    const formData = new FormData();
    formData.append('id_no', idNumber);
    formData.append('_csrf', csrfToken);
    
    // Submit the form
    const submitResponse = await fetch(url, {
      method: 'POST',
      body: formData,
      headers: {
        'Accept': 'text/html',
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    if (!submitResponse.ok) {
      throw new Error(`HTTP error! Status: ${submitResponse.status}`);
    }
    
    const resultHtml = await submitResponse.text();
    return resultHtml;
  } catch (error) {
    console.error('Error checking registration:', error);
    return null;
  }
}

// Example usage
checkTSCRegistration('12345678').then(result => {
  // Parse the result to extract registration status
  // You'll need to analyze the HTML response to extract the relevant information
  console.log('Got response, length:', result?.length);
});