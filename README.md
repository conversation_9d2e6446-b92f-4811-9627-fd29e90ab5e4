# TSC Registration Status Checker

This project provides a solution to check TSC (Teachers Service Commission) registration status while bypassing CORS restrictions.

## Problem Solved

The original code was encountering CORS (Cross-Origin Resource Sharing) errors when trying to access the TSC website directly from a browser due to the same-origin policy. This solution uses a Node.js proxy server to make the requests on behalf of the frontend.

## Files

- `tsc_checker.html` - Main web interface for checking registration status
- `tsc_registration_checker.js` - Standalone JavaScript function for registration checking
- `proxy-server.js` - Node.js proxy server that handles requests to TSC website
- `package.json` - Node.js dependencies

## Setup Instructions

### 1. Install Node.js Dependencies

First, install the required Node.js packages:

```bash
npm install
```

### 2. Start the Proxy Server

Run the proxy server:

```bash
npm start
```

The server will start on `http://localhost:3001`

### 3. Access the Application

Open your browser and go to:
```
http://localhost:3001
```

## How It Works

1. **Proxy Server**: The Node.js server acts as a proxy between your browser and the TSC website
2. **CORS Bypass**: Since the proxy server runs on the backend, it's not subject to browser CORS restrictions
3. **CSRF Handling**: The proxy automatically extracts and includes CSRF tokens required by the TSC website
4. **Response Parsing**: The server attempts to parse the HTML response to extract meaningful registration information

## API Endpoint

The proxy server provides a REST API endpoint:

**POST** `/api/check-registration`

Request body:
```json
{
  "idNumber": "12345678"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "status": "found|not_found|error|unknown",
    "message": "Human readable message",
    "details": {}
  },
  "rawHtml": "First 1000 characters of the response for debugging"
}
```

## Usage Examples

### Using the Web Interface
1. Start the server with `npm start`
2. Open `http://localhost:3001` in your browser
3. Enter an ID/Passport number
4. Click "Check Status"

### Using the JavaScript Function
```javascript
// Make sure you're running this from the same domain as the proxy server
checkTSCRegistration('12345678').then(result => {
  if (result && result.data) {
    console.log('Status:', result.data.status);
    console.log('Message:', result.data.message);
  }
});
```

## Troubleshooting

1. **Port 3001 already in use**: Change the PORT variable in `proxy-server.js`
2. **Network errors**: Check your internet connection and ensure the TSC website is accessible
3. **Parsing issues**: The HTML parsing logic may need adjustment based on changes to the TSC website structure

## Alternative Solutions

If you prefer not to use a proxy server, here are other options:

1. **Browser Extension**: Create a browser extension that can bypass CORS
2. **CORS Browser**: Use a browser with disabled security (not recommended for production)
3. **Desktop Application**: Use Electron or similar to create a desktop app
4. **Server-side Only**: Run the checker entirely on the server without a web interface

## Security Notes

- This proxy server is for development/testing purposes
- For production use, add proper authentication and rate limiting
- Be respectful of the TSC website's resources and terms of service
