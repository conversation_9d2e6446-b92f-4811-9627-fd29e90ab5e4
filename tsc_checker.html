<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TSC Registration Status Checker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        h1 {
            color: #0A6AAA;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #0A6AAA;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #085588;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 100px;
            display: none;
        }
        .loading {
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TSC Registration Status Checker</h1>
        <div class="form-group">
            <label for="idNumber">Enter ID/Passport Number:</label>
            <input type="text" id="idNumber" placeholder="e.g., 12345678">
        </div>
        <button id="checkButton">Check Status</button>
        <div class="loading" id="loading">
            <p>Checking status, please wait...</p>
        </div>
        <div id="result"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const checkButton = document.getElementById('checkButton');
            const idNumberInput = document.getElementById('idNumber');
            const resultDiv = document.getElementById('result');
            const loadingDiv = document.getElementById('loading');

            checkButton.addEventListener('click', async function() {
                const idNumber = idNumberInput.value.trim();
                
                if (!idNumber) {
                    alert('Please enter an ID/Passport number');
                    return;
                }
                
                // Show loading indicator
                loadingDiv.style.display = 'block';
                resultDiv.style.display = 'none';
                
                try {
                    const result = await checkTSCRegistration(idNumber);
                    
                    // Hide loading indicator
                    loadingDiv.style.display = 'none';
                    
                    // Display result
                    resultDiv.style.display = 'block';
                    
                    if (result && result.data) {
                        // Display the parsed registration information
                        const statusColor = result.data.status === 'found' ? '#28a745' :
                                          result.data.status === 'not_found' ? '#ffc107' : '#dc3545';

                        resultDiv.innerHTML = `
                            <h3>Results for ID: ${idNumber}</h3>
                            <div style="padding: 10px; border-left: 4px solid ${statusColor}; background-color: #f8f9fa; margin: 10px 0;">
                                <strong>Status:</strong> <span style="color: ${statusColor}; text-transform: capitalize;">${result.data.status.replace('_', ' ')}</span><br>
                                <strong>Message:</strong> ${result.data.message}
                            </div>
                            ${Object.keys(result.data.details).length > 0 ? `
                                <div style="margin-top: 15px;">
                                    <h4>Additional Details:</h4>
                                    <ul>
                                        ${Object.entries(result.data.details).map(([key, value]) =>
                                            `<li><strong>${key}:</strong> ${value}</li>`
                                        ).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                            <details style="margin-top: 15px;">
                                <summary>View Raw Response (for debugging)</summary>
                                <pre style="background-color: #f1f1f1; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${result.rawHtml}</pre>
                            </details>
                        `;
                    } else {
                        resultDiv.innerHTML = '<p>Error checking registration status. Please try again later.</p>';
                    }
                } catch (error) {
                    loadingDiv.style.display = 'none';
                    resultDiv.style.display = 'block';
                    resultDiv.innerHTML = `<p>Error: ${error.message}</p>`;
                }
            });

            async function checkTSCRegistration(idNumber) {
                try {
                    // Use the proxy server instead of direct request
                    const response = await fetch('/api/check-registration', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ idNumber: idNumber })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (!result.success) {
                        throw new Error(result.error || 'Unknown error occurred');
                    }

                    return result;
                } catch (error) {
                    console.error('Error checking registration:', error);
                    throw error;
                }
            }
        });
    </script>
</body>
</html>