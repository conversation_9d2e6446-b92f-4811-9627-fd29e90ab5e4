<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TSC Registration Status Checker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        h1 {
            color: #0A6AAA;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #0A6AAA;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #085588;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 100px;
            display: none;
        }
        .loading {
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TSC Registration Status Checker</h1>
        <div class="form-group">
            <label for="idNumber">Enter ID/Passport Number:</label>
            <input type="text" id="idNumber" placeholder="e.g., 12345678">
        </div>
        <button id="checkButton">Check Status</button>
        <div class="loading" id="loading">
            <p>Checking status, please wait...</p>
        </div>
        <div id="result"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const checkButton = document.getElementById('checkButton');
            const idNumberInput = document.getElementById('idNumber');
            const resultDiv = document.getElementById('result');
            const loadingDiv = document.getElementById('loading');

            checkButton.addEventListener('click', async function() {
                const idNumber = idNumberInput.value.trim();
                
                if (!idNumber) {
                    alert('Please enter an ID/Passport number');
                    return;
                }
                
                // Show loading indicator
                loadingDiv.style.display = 'block';
                resultDiv.style.display = 'none';
                
                try {
                    const result = await checkTSCRegistration(idNumber);
                    
                    // Hide loading indicator
                    loadingDiv.style.display = 'none';
                    
                    // Display result
                    resultDiv.style.display = 'block';
                    
                    if (result) {
                        // Here you would parse the HTML to extract the relevant information
                        // For now, we'll just show a summary
                        resultDiv.innerHTML = `
                            <h3>Results for ID: ${idNumber}</h3>
                            <p>Response received. You would need to parse the HTML to extract the specific status.</p>
                            <details>
                                <summary>View Raw Response (first 500 chars)</summary>
                                <pre>${result.substring(0, 500)}...</pre>
                            </details>
                        `;
                    } else {
                        resultDiv.innerHTML = '<p>Error checking registration status. Please try again later.</p>';
                    }
                } catch (error) {
                    loadingDiv.style.display = 'none';
                    resultDiv.style.display = 'block';
                    resultDiv.innerHTML = `<p>Error: ${error.message}</p>`;
                }
            });

            async function checkTSCRegistration(idNumber) {
                try {
                    const url = 'http://tsconline.tsc.go.ke/register/registration-status';
                    
                    // Get the CSRF token from the page
                    const response = await fetch(url);
                    const html = await response.text();
                    const csrfMatch = html.match(/<meta name="csrf-token" content="([^"]+)">/);
                    const csrfToken = csrfMatch ? csrfMatch[1] : '';
                    
                    // Create form data with the ID number and CSRF token
                    const formData = new FormData();
                    formData.append('id_no', idNumber);
                    formData.append('_csrf', csrfToken);
                    
                    // Submit the form
                    const submitResponse = await fetch(url, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'Accept': 'text/html'
                        }
                    });
                    
                    if (!submitResponse.ok) {
                        throw new Error(`HTTP error! Status: ${submitResponse.status}`);
                    }
                    
                    const resultHtml = await submitResponse.text();
                    return resultHtml;
                } catch (error) {
                    console.error('Error checking registration:', error);
                    throw error;
                }
            }
        });
    </script>
</body>
</html>