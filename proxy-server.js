const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const path = require('path');

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors());

// Parse JSON and form data
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files (your HTML and JS files)
app.use(express.static('.'));

// Proxy endpoint for TSC registration check
app.post('/api/check-registration', async (req, res) => {
  try {
    const { idNumber } = req.body;
    
    if (!idNumber) {
      return res.status(400).json({ error: 'ID number is required' });
    }

    const tscUrl = 'http://tsconline.tsc.go.ke/register/registration-status';
    
    // First, get the page to extract CSRF token
    console.log('Fetching TSC page for CSRF token...');
    const initialResponse = await fetch(tscUrl);
    const html = await initialResponse.text();
    
    // Extract CSRF token
    const csrfMatch = html.match(/<meta name="csrf-token" content="([^"]+)">/);
    const csrfToken = csrfMatch ? csrfMatch[1] : '';
    
    console.log('CSRF Token found:', csrfToken ? 'Yes' : 'No');
    
    // Prepare form data
    const formData = new URLSearchParams();
    formData.append('id_no', idNumber);
    if (csrfToken) {
      formData.append('_csrf', csrfToken);
    }
    
    // Submit the form
    console.log('Submitting registration check for ID:', idNumber);
    const submitResponse = await fetch(tscUrl, {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    if (!submitResponse.ok) {
      throw new Error(`HTTP error! Status: ${submitResponse.status}`);
    }
    
    const resultHtml = await submitResponse.text();
    
    // Try to parse the result to extract meaningful information
    const registrationInfo = parseRegistrationResult(resultHtml);
    
    res.json({
      success: true,
      data: registrationInfo,
      rawHtml: resultHtml.substring(0, 1000) // First 1000 chars for debugging
    });
    
  } catch (error) {
    console.error('Error checking registration:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Function to parse the registration result
function parseRegistrationResult(html) {
  // This is a basic parser - you may need to adjust based on the actual HTML structure
  const result = {
    status: 'unknown',
    message: '',
    details: {}
  };
  
  // Look for common patterns in the response
  if (html.includes('registered') || html.includes('Registration Status')) {
    result.status = 'found';
    result.message = 'Registration information found';
  } else if (html.includes('not found') || html.includes('No record')) {
    result.status = 'not_found';
    result.message = 'No registration record found';
  } else if (html.includes('error') || html.includes('Error')) {
    result.status = 'error';
    result.message = 'Error occurred while checking registration';
  }
  
  // You can add more specific parsing logic here based on the actual HTML structure
  // For example, extracting specific fields like name, registration number, etc.
  
  return result;
}

// Serve the main HTML file
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'tsc_checker.html'));
});

app.listen(PORT, () => {
  console.log(`Proxy server running on http://localhost:${PORT}`);
  console.log('Open your browser and go to http://localhost:3000 to use the TSC checker');
});
